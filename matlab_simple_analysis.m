%% C题问题一：多元线性回归与斯皮尔曼相关性分析 - 简化版本
% 作者：AI团队
% 日期：2025年

clear; clc; close all;

fprintf('=== C题问题一：多元线性回归与斯皮尔曼相关性分析 ===\n');

%% 1. 数据读取与预处理
fprintf('\n1. 数据读取与预处理\n');

% 尝试读取Excel文件
filename = '附件666666.xlsx';
try
    % 使用xlsread读取数值数据
    [num_data, txt_data, raw_data] = xlsread(filename);
    fprintf('数据读取成功！数据维度：%d行 × %d列\n', size(num_data, 1), size(num_data, 2));
    
    % 创建变量名（简化版本）
    var_names = {};
    for i = 1:size(num_data, 2)
        var_names{i} = sprintf('Var%d', i);
    end
    
    % 处理缺失值
    fprintf('处理缺失值...\n');
    before_rows = size(num_data, 1);
    
    % 删除包含NaN的行
    valid_rows = ~any(isnan(num_data), 2);
    clean_data = num_data(valid_rows, :);
    
    after_rows = size(clean_data, 1);
    fprintf('删除缺失值后：从%d行减少到%d行\n', before_rows, after_rows);
    
catch ME
    fprintf('数据读取失败：%s\n', ME.message);
    return;
end

%% 2. 斯皮尔曼相关性分析
fprintf('\n2. 斯皮尔曼相关性分析\n');

% 计算斯皮尔曼相关系数
[rho, pval] = corr(clean_data, 'Type', 'Spearman', 'Rows', 'complete');

fprintf('斯皮尔曼相关性分析完成\n');
fprintf('相关系数矩阵维度：%d × %d\n', size(rho, 1), size(rho, 2));

% 显示显著相关性（|rho| > 0.5 且 p < 0.05）
fprintf('\n显著相关性对（|rho| > 0.5 且 p < 0.05）：\n');
significant_count = 0;
for i = 1:length(var_names)
    for j = i+1:length(var_names)
        if abs(rho(i,j)) > 0.5 && pval(i,j) < 0.05
            fprintf('%s - %s: rho = %.3f, p = %.4f\n', ...
                   var_names{i}, var_names{j}, rho(i,j), pval(i,j));
            significant_count = significant_count + 1;
        end
    end
end
fprintf('共发现%d个显著相关性对\n', significant_count);

%% 3. 多元线性回归分析
fprintf('\n3. 多元线性回归分析\n');

% 假设第5列为目标变量（根据数据结构调整）
if size(clean_data, 2) >= 5
    target_col = 5;  % 假设是孕妇BMI
    y = clean_data(:, target_col);
    
    % 选择预测变量（排除目标变量）
    predictor_cols = setdiff(1:size(clean_data, 2), target_col);
    X = clean_data(:, predictor_cols);
    
    fprintf('目标变量：第%d列\n', target_col);
    fprintf('预测变量数量：%d\n', length(predictor_cols));
    
    % 标准化预测变量
    X_std = zscore(X);
    
    % 添加截距项
    X_with_intercept = [ones(size(X_std, 1), 1), X_std];
    
    % 多元线性回归
    beta = X_with_intercept \ y;  % 最小二乘法求解
    
    % 预测值
    y_pred = X_with_intercept * beta;
    
    % 计算回归统计量
    n = length(y);
    p = size(X_with_intercept, 2);
    
    % 残差
    residuals = y - y_pred;
    
    % R平方
    SST = sum((y - mean(y)).^2);  % 总平方和
    SSE = sum(residuals.^2);      % 残差平方和
    SSR = SST - SSE;              % 回归平方和
    R_squared = SSR / SST;
    R_squared_adj = 1 - (SSE/(n-p)) / (SST/(n-1));  % 调整R平方
    
    % 标准误差
    MSE = SSE / (n - p);
    
    % F统计量
    F_stat = (SSR / (p-1)) / (SSE / (n-p));
    F_pvalue = 1 - fcdf(F_stat, p-1, n-p);
    
    % 显示回归结果
    fprintf('\n多元线性回归结果：\n');
    fprintf('R² = %.4f\n', R_squared);
    fprintf('调整R² = %.4f\n', R_squared_adj);
    fprintf('F统计量 = %.4f, p值 = %.6f\n', F_stat, F_pvalue);
    fprintf('均方误差 = %.4f\n', MSE);
    
    % 显示重要系数（绝对值最大的前10个）
    beta_abs = abs(beta(2:end));  % 排除截距
    [sorted_beta, sort_idx] = sort(beta_abs, 'descend');
    
    fprintf('\n最重要的预测变量（按系数绝对值排序）：\n');
    for i = 1:min(10, length(sort_idx))
        var_idx = predictor_cols(sort_idx(i));
        fprintf('变量%d: 系数 = %.4f\n', var_idx, beta(sort_idx(i)+1));
    end
    
else
    fprintf('数据列数不足，无法进行回归分析\n');
end

%% 4. 多目标变量分析
fprintf('\n4. 多目标变量分析\n');

% 对多个可能的目标变量进行分析
target_candidates = [3, 5, 11, 12, 13];  % 假设的目标变量列
target_candidates = target_candidates(target_candidates <= size(clean_data, 2));

for t = 1:length(target_candidates)
    target_col = target_candidates(t);
    y_current = clean_data(:, target_col);
    
    % 选择预测变量
    predictor_cols_current = setdiff(1:size(clean_data, 2), target_col);
    X_current = clean_data(:, predictor_cols_current);
    
    % 标准化并添加截距
    X_current_std = zscore(X_current);
    X_current_with_intercept = [ones(size(X_current_std, 1), 1), X_current_std];
    
    % 回归分析
    beta_current = X_current_with_intercept \ y_current;
    y_pred_current = X_current_with_intercept * beta_current;
    
    % 计算R²
    SST_current = sum((y_current - mean(y_current)).^2);
    SSE_current = sum((y_current - y_pred_current).^2);
    R_squared_current = 1 - SSE_current / SST_current;
    
    % 调整R²
    n_current = length(y_current);
    p_current = size(X_current_with_intercept, 2);
    R_squared_adj_current = 1 - (SSE_current/(n_current-p_current)) / (SST_current/(n_current-1));
    
    fprintf('目标变量%d: R² = %.4f, 调整R² = %.4f\n', ...
           target_col, R_squared_current, R_squared_adj_current);
end

%% 5. 结果总结
fprintf('\n=== 分析结果总结 ===\n');
fprintf('分析日期：%s\n', datestr(now));
fprintf('数据文件：%s\n', filename);
fprintf('有效样本数：%d\n', after_rows);
fprintf('分析变量数：%d\n', size(clean_data, 2));
fprintf('显著相关性对数：%d\n', significant_count);

if exist('R_squared', 'var')
    fprintf('主要回归分析R²：%.4f\n', R_squared);
    fprintf('主要回归分析调整R²：%.4f\n', R_squared_adj);
end

fprintf('\n=== MATLAB分析完成 ===\n');
fprintf('结果可与Python版本进行对比验证\n');
