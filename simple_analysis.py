#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C题问题一：多元线性回归与斯皮尔曼相关性分析 - 简化版本
"""

import pandas as pd
import numpy as np
from scipy.stats import spearmanr
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== C题问题一：多元线性回归与斯皮尔曼相关性分析 ===")
    
    # 1. 数据读取
    print("\n1. 数据读取与预处理")
    try:
        data = pd.read_excel('附件666666.xlsx')
        print(f"数据读取成功！数据维度：{data.shape[0]}行 × {data.shape[1]}列")
    except Exception as e:
        print(f"数据读取失败：{e}")
        return
    
    # 选择数值型变量
    numeric_vars = ['年龄', '身高', '体重', '检测孕周', '孕妇BMI', '原始读段数',
                   '在参考基因组上比对的比例', '重复读段的比例', '唯一比对的读段数',
                   'GC含量', '13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值',
                   'X染色体的Z值', 'Y染色体的Z值', 'Y染色体浓度', 'X染色体浓度',
                   '13号染色体的GC含量', '18号染色体的GC含量', '21号染色体的GC含量',
                   '被过滤掉读段数的比例', '生产次数']
    
    # 提取存在的数值型变量
    available_vars = [var for var in numeric_vars if var in data.columns]
    numeric_data = data[available_vars].copy()
    
    # 处理缺失值
    before_rows = len(numeric_data)
    numeric_data = numeric_data.dropna()
    after_rows = len(numeric_data)
    print(f"删除缺失值后：从{before_rows}行减少到{after_rows}行")
    
    # 2. 斯皮尔曼相关性分析
    print("\n2. 斯皮尔曼相关性分析")
    corr_matrix = numeric_data.corr(method='spearman')
    print(f"相关系数矩阵维度：{corr_matrix.shape}")
    
    # 找出显著相关性
    significant_pairs = []
    var_names = numeric_data.columns.tolist()
    
    print("\n显著相关性对（|rho| > 0.5）：")
    for i in range(len(var_names)):
        for j in range(i+1, len(var_names)):
            rho = corr_matrix.iloc[i, j]
            if abs(rho) > 0.5:
                print(f"{var_names[i]} - {var_names[j]}: rho = {rho:.3f}")
                significant_pairs.append({
                    'Variable1': var_names[i],
                    'Variable2': var_names[j],
                    'SpearmanRho': rho
                })
    
    # 保存相关性结果
    if significant_pairs:
        correlation_df = pd.DataFrame(significant_pairs)
        correlation_df.to_excel('python_correlation_results.xlsx', index=False)
        print(f"\n保存{len(significant_pairs)}个显著相关性对到python_correlation_results.xlsx")
    
    # 3. 多元线性回归分析
    print("\n3. 多元线性回归分析")
    target_var = '孕妇BMI'
    
    if target_var not in numeric_data.columns:
        print(f"未找到目标变量：{target_var}")
        return
    
    # 准备数据
    y = numeric_data[target_var].values
    X = numeric_data.drop(columns=[target_var]).values
    predictor_vars = numeric_data.drop(columns=[target_var]).columns.tolist()
    
    print(f"目标变量：{target_var}")
    print(f"预测变量数量：{len(predictor_vars)}")
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 拟合模型
    model = LinearRegression()
    model.fit(X_scaled, y)
    
    # 预测
    y_pred = model.predict(X_scaled)
    
    # 计算统计量
    r2 = r2_score(y, y_pred)
    mse = mean_squared_error(y, y_pred)
    n = len(y)
    p = X_scaled.shape[1] + 1  # 包括截距
    
    # 调整R²
    r2_adj = 1 - (1 - r2) * (n - 1) / (n - p)
    
    print(f"\n多元线性回归结果：")
    print(f"R² = {r2:.4f}")
    print(f"调整R² = {r2_adj:.4f}")
    print(f"均方误差 = {mse:.4f}")
    
    # 创建系数表
    coefficients = np.concatenate([[model.intercept_], model.coef_])
    var_names_with_intercept = ['截距'] + predictor_vars
    
    coeff_df = pd.DataFrame({
        'Variable': var_names_with_intercept,
        'Coefficient': coefficients,
        'AbsCoefficient': np.abs(coefficients)
    })
    
    # 按系数绝对值排序
    coeff_df = coeff_df.sort_values('AbsCoefficient', ascending=False)
    
    print(f"\n回归系数（按绝对值排序，前10个）：")
    print(coeff_df.head(10)[['Variable', 'Coefficient']])
    
    # 保存结果
    coeff_df.to_excel('python_regression_coefficients.xlsx', index=False)
    
    # 4. 变量重要性分析
    print("\n4. 变量重要性分析")
    importance_df = coeff_df[coeff_df['Variable'] != '截距'].copy()
    importance_df['ImportanceScore'] = importance_df['AbsCoefficient']
    
    print("变量重要性排序（前10个）：")
    print(importance_df.head(10)[['Variable', 'ImportanceScore']])
    
    # 保存重要性结果
    importance_df.to_excel('python_variable_importance.xlsx', index=False)
    
    # 5. 多目标变量分析
    print("\n5. 多目标变量分析")
    target_candidates = ['孕妇BMI', '13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值']
    regression_summary = []
    
    for target in target_candidates:
        if target in numeric_data.columns:
            y_current = numeric_data[target].values
            X_current = numeric_data.drop(columns=[target]).values
            
            # 标准化并拟合
            X_current_scaled = StandardScaler().fit_transform(X_current)
            model_current = LinearRegression()
            model_current.fit(X_current_scaled, y_current)
            
            # 预测和评估
            y_pred_current = model_current.predict(X_current_scaled)
            r2_current = r2_score(y_current, y_pred_current)
            
            # 调整R²
            n_current = len(y_current)
            p_current = X_current_scaled.shape[1] + 1
            r2_adj_current = 1 - (1 - r2_current) * (n_current - 1) / (n_current - p_current)
            
            regression_summary.append({
                'TargetVariable': target,
                'R_Squared': r2_current,
                'R_Squared_Adj': r2_adj_current,
                'NumPredictors': X_current_scaled.shape[1]
            })
            
            print(f"{target}: R² = {r2_current:.4f}, 调整R² = {r2_adj_current:.4f}")
    
    # 保存多目标回归摘要
    summary_df = pd.DataFrame(regression_summary)
    summary_df.to_excel('python_multi_target_summary.xlsx', index=False)
    
    print(f"\n=== 分析完成 ===")
    print("输出文件：")
    print("1. python_correlation_results.xlsx - 相关性分析结果")
    print("2. python_regression_coefficients.xlsx - 回归系数")
    print("3. python_variable_importance.xlsx - 变量重要性")
    print("4. python_multi_target_summary.xlsx - 多目标回归摘要")

if __name__ == "__main__":
    main()
